name: CI/CD Pipeline for Prod

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      ACCESS_TOKEN_SECRET_KEY: ${{ secrets.ACCESS_TOKEN_SECRET_KEY }}
      XHS_SOURCE_BUCKET_NAME: ${{ secrets.XHS_SOURCE_BUCKET_NAME }}
      XHS_AWS_REGION: ${{ secrets.XHS_AWS_REGION }}
      PHOTO_SUMMARY_QUEUE_URL: ${{ secrets.PHOTO_SUMMARY_QUEUE_URL }}
      AZURE_AI_41_KEY: ${{ secrets.AZURE_AI_41_KEY }}
      AZURE_AI_41_ENDPOINT: ${{ secrets.AZURE_AI_41_ENDPOINT }}
      MILVUS_KEY: ${{ secrets.MILVUS_KEY }}
      MILVUS_URI: ${{ secrets.MILVUS_URI }}
      AZURE_DIC_KEY: ${{ secrets.AZURE_DIC_KEY }}
      AZURE_DIC_ENDPOINT: ${{ secrets.AZURE_DIC_ENDPOINT }}
      FILE_PAGE_IMG_QUEUE: ${{ secrets.FILE_PAGE_IMG_QUEUE }}
      FILE_INDEX_QUEUE: ${{ secrets.FILE_INDEX_QUEUE }}
      AZURE_EMBEDDING_AI_KEY: ${{ secrets.AZURE_EMBEDDING_AI_KEY }}
      AZURE_EMBEDDING_AI_ENDPOINT: ${{ secrets.AZURE_EMBEDDING_AI_ENDPOINT }}
      GENERATE_QUEUE_URL: ${{ secrets.GENERATE_QUEUE_URL }}
      KEYWORD_GENERATION_QUEUE_URL: ${{ secrets.KEYWORD_GENERATION_QUEUE_URL }}
      VECTOR_SEARCH_QUEUE_URL: ${{ secrets.VECTOR_SEARCH_QUEUE_URL }}
      DATA_MERGE_QUEUE_URL: ${{ secrets.DATA_MERGE_QUEUE_URL }}
      CONTENT_GENERATION_QUEUE_URL: ${{ secrets.CONTENT_GENERATION_QUEUE_URL }}
      REQUIRED_VARS: |
        ACCESS_TOKEN_SECRET_KEY
        XHS_SOURCE_BUCKET_NAME
        XHS_AWS_REGION
        PHOTO_SUMMARY_QUEUE_URL
        AZURE_AI_41_KEY
        AZURE_AI_41_ENDPOINT
        MILVUS_KEY
        MILVUS_URI
        AZURE_DIC_KEY
        AZURE_DIC_ENDPOINT
        FILE_PAGE_IMG_QUEUE
        FILE_INDEX_QUEUE
        AZURE_EMBEDDING_AI_KEY
        AZURE_EMBEDDING_AI_ENDPOINT
        GENERATE_QUEUE_URL
        KEYWORD_GENERATION_QUEUE_URL
        VECTOR_SEARCH_QUEUE_URL
        DATA_MERGE_QUEUE_URL
        CONTENT_GENERATION_QUEUE_URL

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '22'

      - name: Validate required environment variables
        run: |
          missing_vars=()
          for var in $REQUIRED_VARS; do
            if [ -z "${!var}" ]; then
              missing_vars+=("$var")
            fi
          done
          if [ ${#missing_vars[@]} -ne 0 ]; then
            echo "错误：以下环境变量未设置或为空："
            for var in "${missing_vars[@]}"; do
              echo "  - $var"
            done
            exit 1
          fi
        shell: bash

      - name: Install dependencies
        run: npm install

      - name: Run ESLint
        run: npm run lint

      - name: Create .env file
        run: |
          for var in $REQUIRED_VARS; do
            echo "$var=${!var}" >> .env
          done

      - name: Deploy to AWS
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          SERVERLESS_ACCESS_KEY: AKXuEgbRTWMYYfgbnJgWEHO4IB6knI27BJbxM1tgsxQ7s
        run: npx sls deploy --region=ap-southeast-1 --stage=production
