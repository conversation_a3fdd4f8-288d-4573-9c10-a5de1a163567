service: xhs-service

frameworkVersion: '4'
build:
  esbuild:
    external:
      - '@zilliz/milvus2-sdk-node'
    bundle: true
    minify: false
    sourcemap: true
    exclude:
      - '@aws-sdk/*'
custom:
  sharp-layer-arn:
    dev: arn:aws:lambda:ap-southeast-1:881490090239:layer:sharp:1
    production: arn:aws:lambda:ap-southeast-1:881490090239:layer:sharp:2
  gm-layer-arn:
    dev: arn:aws:lambda:ap-southeast-1:175033217214:layer:graphicsmagick:2
    production: arn:aws:lambda:ap-southeast-1:175033217214:layer:graphicsmagick:2
  gs-layer-arn:
    dev: arn:aws:lambda:ap-southeast-1:764866452798:layer:ghostscript:17
    production: arn:aws:lambda:ap-southeast-1:764866452798:layer:ghostscript:17
  photo-summary-queue-arn:
    dev: arn:aws:sqs:ap-southeast-1:881490090239:XhsPhotoSummaryQueue
    production: arn:aws:sqs:ap-southeast-1:881490090239:XhsPhotoSummaryQueue
  file-index-queue-arn:
    dev: arn:aws:sqs:ap-southeast-1:881490090239:XhsFileIndexQueue
    production: arn:aws:sqs:ap-southeast-1:881490090239:XhsFileIndexQueue
  file-page-img-queue-arn:
    dev: arn:aws:sqs:ap-southeast-1:881490090239:XhsFilePageImgQueue
    production: arn:aws:sqs:ap-southeast-1:881490090239:XhsFilePageImgQueue
  generate-queue-arn:
    dev: arn:aws:sqs:ap-southeast-1:881490090239:XhsGenerateQueue
    production: arn:aws:sqs:ap-southeast-1:881490090239:XhsGenerateQueue
  keyword-generation-queue-arn:
    dev: arn:aws:sqs:ap-southeast-1:881490090239:XhsKeywordGenerationQueue
    production: arn:aws:sqs:ap-southeast-1:881490090239:XhsKeywordGenerationQueue
  vector-search-queue-arn:
    dev: arn:aws:sqs:ap-southeast-1:881490090239:XhsVectorSearchQueue
    production: arn:aws:sqs:ap-southeast-1:881490090239:XhsVectorSearchQueue
  data-merge-queue-arn:
    dev: arn:aws:sqs:ap-southeast-1:881490090239:XhsDataMergeQueue
    production: arn:aws:sqs:ap-southeast-1:881490090239:XhsDataMergeQueue
  content-generation-queue-arn:
    dev: arn:aws:sqs:ap-southeast-1:881490090239:XhsContentGenerationQueue
    production: arn:aws:sqs:ap-southeast-1:881490090239:XhsContentGenerationQueue

provider:
  name: aws
  runtime: nodejs20.x
  region: ap-southeast-1
  memorySize: 128 # 默认轻量级API函数内存
  timeout: 29
  stage: production
  websocketsApiRouteSelectionExpression: $request.body.action
  logs:
    httpApi: true
  environment:
    ACCESS_TOKEN_SECRET_KEY: ${env:ACCESS_TOKEN_SECRET_KEY}
    XHS_SOURCE_BUCKET_NAME: ${env:XHS_SOURCE_BUCKET_NAME}
    XHS_AWS_REGION: ${env:XHS_AWS_REGION}
    PHOTO_SUMMARY_QUEUE_URL: ${env:PHOTO_SUMMARY_QUEUE_URL}
    AZURE_AI_41_KEY: ${env:AZURE_AI_41_KEY}
    AZURE_AI_41_ENDPOINT: ${env:AZURE_AI_41_ENDPOINT}
    MILVUS_KEY: ${env:MILVUS_KEY}
    MILVUS_URI: ${env:MILVUS_URI}
    AZURE_DIC_KEY: ${env:AZURE_DIC_KEY}
    AZURE_DIC_ENDPOINT: ${env:AZURE_DIC_ENDPOINT}
    FILE_PAGE_IMG_QUEUE: ${env:FILE_PAGE_IMG_QUEUE}
    FILE_INDEX_QUEUE: ${env:FILE_INDEX_QUEUE}
    AZURE_EMBEDDING_AI_KEY: ${env:AZURE_EMBEDDING_AI_KEY}
    AZURE_EMBEDDING_AI_ENDPOINT: ${env:AZURE_EMBEDDING_AI_ENDPOINT}
    GENERATE_QUEUE_URL: ${env:GENERATE_QUEUE_URL}
    KEYWORD_GENERATION_QUEUE_URL: ${env:KEYWORD_GENERATION_QUEUE_URL}
    VECTOR_SEARCH_QUEUE_URL: ${env:VECTOR_SEARCH_QUEUE_URL}
    DATA_MERGE_QUEUE_URL: ${env:DATA_MERGE_QUEUE_URL}
    CONTENT_GENERATION_QUEUE_URL: ${env:CONTENT_GENERATION_QUEUE_URL}
    WEBSOCKET_ENDPOINT: ${env:WEBSOCKET_ENDPOINT}

  httpApi:
    cors:
      allowedOrigins:
        - '*' # 允许所有来源
      allowedHeaders:
        - Content-Type
        - X-Amz-Date
        - Authorization
        - X-Api-Key
        - X-Amz-Security-Token
        - X-Amz-User-Agent
      allowedMethods:
        - OPTIONS
        - POST
    authorizers:
      myAuthorizer:
        type: request
        functionName: requestAuthorizer
        enableSimpleResponses: true
        resultTtlInSeconds: 0
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - dynamodb:ListTables
            - dynamodb:Scan
            - dynamodb:Query
            - dynamodb:GetItem
            - dynamodb:PutItem
            - dynamodb:UpdateItem
            - dynamodb:DeleteItem
            - dynamodb:BatchWriteItem
          Resource: '*'
        - Effect: Allow
          Action: ['s3:*']
          Resource: '*'
        - Effect: Allow
          Action: ['sqs:*']
          Resource: '*'
        - Effect: Allow
          Action: [bedrock:*]
          Resource: '*'

package:
  individually: true

functions:
  #websocket
  websocketConnect:
    handler: src/handlers/websocket/connect.handler
    events:
      - websocket:
          route: $connect
  websocketDisconnect:
    handler: src/handlers/websocket/disconnect.handler
    events:
      - websocket:
          route: $disconnect
  # ===== Authentication Functions =====
  # 自定义请求授权器
  requestAuthorizer:
    handler: src/handlers/services/requestAuth/index.handler
  apiAuthRegister:
    handler: src/handlers/api/auth/register.handler
    memorySize: 256
    events:
      - httpApi:
          method: POST
          path: /api/auth/register
  apiAuthMe:
    handler: src/handlers/api/auth/me.handler
    memorySize: 128
    events:
      - httpApi:
          method: POST
          path: /api/auth/me
          authorizer:
            name: myAuthorizer

  apiAuthVerifyToken:
    handler: src/handlers/api/auth/verify-token.handler
    memorySize: 128 # 轻量级token验证
    events:
      - httpApi:
          method: POST
          path: /api/auth/verify-token
  # ===== Persona Functions =====
  apiPersonaCreate:
    handler: src/handlers/api/persona/create.handler
    memorySize: 128 # 轻量级token验证
    events:
      - httpApi:
          method: POST
          path: /api/persona/create
          authorizer:
            name: myAuthorizer

  apiPersonaList:
    handler: src/handlers/api/persona/list.handler
    memorySize: 128 # 轻量级查询
    events:
      - httpApi:
          method: POST
          path: /api/persona/list
          authorizer:
            name: myAuthorizer

  apiPersonaUpdate:
    handler: src/handlers/api/persona/update.handler
    memorySize: 128 # 轻量级更新
    events:
      - httpApi:
          method: POST
          path: /api/persona/update
          authorizer:
            name: myAuthorizer

  apiPersonaSetDefault:
    handler: src/handlers/api/persona/set-default.handler
    memorySize: 128 # 轻量级更新
    events:
      - httpApi:
          method: POST
          path: /api/persona/set-default
          authorizer:
            name: myAuthorizer

  # ===== Photo Functions =====
  apiPhotoUpload:
    handler: src/handlers/api/photo/upload.handler
    memorySize: 256 # 照片处理需要更多内存
    events:
      - httpApi:
          method: POST
          path: /api/photo/upload
          authorizer:
            name: myAuthorizer

  apiPhotoList:
    handler: src/handlers/api/photo/list.handler
    memorySize: 256 # 照片列表查询需要适中内存
    events:
      - httpApi:
          method: POST
          path: /api/photo/list
          authorizer:
            name: myAuthorizer

  apiPhotoStatistics:
    handler: src/handlers/api/photo/statistics.handler
    memorySize: 256 # 照片统计查询需要适中内存
    events:
      - httpApi:
          method: POST
          path: /api/photo/statistics
          authorizer:
            name: myAuthorizer

  apiPhotoFilter:
    handler: src/handlers/api/photo/filter.handler
    memorySize: 256 # 照片筛选查询需要适中内存
    events:
      - httpApi:
          method: POST
          path: /api/photo/filter
          authorizer:
            name: myAuthorizer
  apiPhotoDelete:
    handler: src/handlers/api/photo/delete.handler
    memorySize: 256 # 照片删除需要适中内存
    events:
      - httpApi:
          method: POST
          path: /api/photo/delete
          authorizer:
            name: myAuthorizer

  # ===== File Functions =====
  apiFileImport:
    handler: src/handlers/api/file/import.handler
    memorySize: 256 # 文件导入需要适中内存
    events:
      - httpApi:
          method: POST
          path: /api/file/import
          authorizer:
            name: myAuthorizer
  apiFileList:
    handler: src/handlers/api/file/list.handler
    memorySize: 256 # 文件列表查询需要适中内存
    events:
      - httpApi:
          method: POST
          path: /api/file/list
          authorizer:
            name: myAuthorizer
  apiFileDelete:
    handler: src/handlers/api/file/delete.handler
    memorySize: 1024 # 文件删除需要适中内存
    timeout: 60 # 增加超时时间以支持复杂删除操作
    events:
      - httpApi:
          method: POST
          path: /api/file/delete
          authorizer:
            name: myAuthorizer

  # ===== Source Functions =====
  apiSourceCreate:
    handler: src/handlers/api/source/create.handler
    memorySize: 128 # 轻量级创建操作
    events:
      - httpApi:
          method: POST
          path: /api/source/create
          authorizer:
            name: myAuthorizer

  apiSourceList:
    handler: src/handlers/api/source/list.handler
    memorySize: 128 # 轻量级查询操作
    events:
      - httpApi:
          method: POST
          path: /api/source/list
          authorizer:
            name: myAuthorizer

  apiSourceUpdateConnection:
    handler: src/handlers/api/source/update-connection.handler
    memorySize: 128 # 轻量级更新操作
    events:
      - httpApi:
          method: POST
          path: /api/source/update-connection
          authorizer:
            name: myAuthorizer

  apiSourceDelete:
    handler: src/handlers/api/source/delete.handler
    memorySize: 128 # 轻量级删除操作
    events:
      - httpApi:
          method: POST
          path: /api/source/delete
          authorizer:
            name: myAuthorizer

  # 文件索引处理器
  fileIndexProcessor:
    handler: src/handlers/services/fileIndex/index.handler
    memorySize: 512 # AI处理需要更多内存
    timeout: 300 # 5分钟超时，AI调用可能较慢
    events:
      - sqs:
          arn: ${self:custom.file-index-queue-arn.${self:provider.stage}}
          batchSize: 5 # 批量处理5个任务

  # 文件页面图片生成处理器
  fileImgHandler:
    handler: src/handlers/services/fileImg/index.handler
    memorySize: 1024 # 图片处理需要更多内存
    timeout: 900 # 15分钟超时，图片生成可能较慢
    layers:
      - ${self:custom.sharp-layer-arn.${self:provider.stage}}
      - ${self:custom.gm-layer-arn.${self:provider.stage}}
      - ${self:custom.gs-layer-arn.${self:provider.stage}}
    events:
      - sqs:
          arn: ${self:custom.file-page-img-queue-arn.${self:provider.stage}}
          batchSize: 1 # 图片处理单个处理

  # 照片摘要生成处理器
  photoSummaryProcessor:
    handler: src/handlers/services/photoSummary/processor.handler
    memorySize: 512 # AI处理需要更多内存
    timeout: 300 # 5分钟超时，AI调用可能较慢
    events:
      - sqs:
          arn: ${self:custom.photo-summary-queue-arn.${self:provider.stage}}
          batchSize: 5 # 批量处理5个任务

  # ===== AI Generation Functions =====
  # AI生成任务处理器
  aiGenerationProcessor:
    handler: src/handlers/services/aiGeneration/processor.handler
    memorySize: 512
    timeout: 300
    events:
      - sqs:
          arn: ${self:custom.generate-queue-arn.${self:provider.stage}}
          batchSize: 1 # 单个处理生成任务
  # AI关键词生成处理器
  aiKeywordGenerationProcessor:
    handler: src/handlers/services/aiGeneration/keywordProcessor.handler
    memorySize: 512 # 关键词生成需要更多内存
    timeout: 600 # 5分钟超时，AI调用可能较慢
    events:
      - sqs:
          arn: ${self:custom.keyword-generation-queue-arn.${self:provider.stage}}
          batchSize: 1 # 单个处理关键词生成任务
  # AI向量搜索处理器
  aiVectorSearchProcessor:
    handler: src/handlers/services/aiGeneration/vectorProcessor.handler
    memorySize: 512 # 向量搜索需要更多内存
    timeout: 600 # 10分钟超时，向量搜索可能较慢
    events:
      - sqs:
          arn: ${self:custom.vector-search-queue-arn.${self:provider.stage}}
          batchSize: 1 # 单个处理向量搜索任务
  # AI数据合并处理器
  aiDataMergeProcessor:
    handler: src/handlers/services/aiGeneration/dataMergeProcessor.handler
    memorySize: 512 # 数据合并需要更多内存
    timeout: 600 # 10分钟超时，数据合并可能较慢
    events:
      - sqs:
          arn: ${self:custom.data-merge-queue-arn.${self:provider.stage}}
          batchSize: 1 # 单个处理数据合并任务
  # AI内容生成处理器
  aiContentGenerationProcessor:
    handler: src/handlers/services/aiGeneration/generateProcessor.handler
    memorySize: 512 # 内容生成需要更多内存
    timeout: 600 # 10分钟超时，内容生成可能较慢
    events:
      - sqs:
          arn: ${self:custom.content-generation-queue-arn.${self:provider.stage}}
          batchSize: 1 # 单个处理内容生成任务
  # ===== API Functions =====

  # # ===== File Upload Functions =====
  apiUploadStart:
    handler: src/handlers/api/upload/start.handler
    memorySize: 128 # 轻量级上传初始化
    events:
      - httpApi:
          method: POST
          path: /api/upload/start
          authorizer:
            name: myAuthorizer

  apiUploadPresign:
    handler: src/handlers/api/upload/presign.handler
    memorySize: 128 # 轻量级预签名生成
    events:
      - httpApi:
          method: POST
          path: /api/upload/presign
          authorizer:
            name: myAuthorizer

  apiUploadComplete:
    handler: src/handlers/api/upload/complete.handler
    memorySize: 256 # 上传完成处理
    events:
      - httpApi:
          method: POST
          path: /api/upload/complete
          authorizer:
            name: myAuthorizer

  # ===== AI Generation API Functions =====
  # AI生成任务创建
  apiAiGenerate:
    handler: src/handlers/api/ai/generate.handler
    memorySize: 256 # AI生成任务创建需要适中内存
    events:
      - httpApi:
          method: POST
          path: /api/ai/generate
          authorizer:
            name: myAuthorizer

  # AI生成任务结果查询
  apiAiGenerateResult:
    handler: src/handlers/api/ai/result.handler
    memorySize: 256 # 结果查询需要适中内存
    events:
      - httpApi:
          method: POST
          path: /api/ai/generateResult
          authorizer:
            name: myAuthorizer
  # AI生成列表查询
  apiAiGenerateList:
    handler: src/handlers/api/ai/generateList.handler
    memorySize: 256 # 生成列表查询需要适中内存
    events:
      - httpApi:
          method: POST
          path: /api/ai/generateList
          authorizer:
            name: myAuthorizer
  # getList
  apiAiGenerateGetList:
    handler: src/handlers/api/ai/getList.handler
    memorySize: 256 # 生成列表获取需要适中内存
    events:
      - httpApi:
          method: POST
          path: /api/ai/generateGetList
          authorizer:
            name: myAuthorizer

  # AI生成列表更新
  apiAiGenerateUpdate:
    handler: src/handlers/api/ai/update.handler
    memorySize: 256 # 生成列表更新需要适中内存
    events:
      - httpApi:
          method: POST
          path: /api/ai/generateUpdate
          authorizer:
            name: myAuthorizer
  # getGenerate
  apiAiGenerateGetGenerate:
    handler: src/handlers/api/ai/getGenerate.handler
    memorySize: 256 # 生成列表获取需要适中内存
    events:
      - httpApi:
          method: POST
          path: /api/ai/generateGetGenerate
          authorizer:
            name: myAuthorizer
  # AI生成统计
  apiAiGenerateStatistics:
    handler: src/handlers/api/ai/statistics.handler
    memorySize: 256 # 生成统计需要适中内存
    events:
      - httpApi:
          method: POST
          path: /api/ai/generateStatistics
          authorizer:
            name: myAuthorizer

  # ===== AI Configuration Functions =====
  apiAiConfigCreate:
    handler: src/handlers/api/ai/config.handler
    memorySize: 128 # 轻量级配置创建
    events:
      - httpApi:
          method: POST
          path: /api/ai/config
          authorizer:
            name: myAuthorizer
  apiAiConfigGet:
    handler: src/handlers/api/ai/get.handler
    memorySize: 128 # 轻量级配置获取
    events:
      - httpApi:
          method: POST
          path: /api/ai/get
          authorizer:
            name: myAuthorizer
  loginOauth:
    handler: src/handlers/api/auth/Oauth.handler
